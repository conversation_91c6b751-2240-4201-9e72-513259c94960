#!/usr/bin/env python3
"""
PC Optimizer for Low-End Systems
Specifically optimized for Intel i3-5005U and HD Graphics 5500
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import os
import sys
import subprocess
import threading
import time
import shutil
import tempfile
import winreg
from pathlib import Path

class PCOptimizer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("PC Optimizer - Low-End Edition")
        self.root.geometry("1000x800")  # Größeres Fenster
        self.root.configure(bg="#2b2b2b")

        # Fenster zentrieren
        self.center_window()

        # Minimum size setzen
        self.root.minsize(900, 700)

        # Configure styling
        self.style = ttk.Style()
        self.style.theme_use("clam")
        self.configure_styles()

        self.setup_ui()

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def configure_styles(self):
        """Configure clean and modern UI styles"""
        self.style.configure("Title.TLabel",
                           background="#2b2b2b",
                           foreground="#ffffff",
                           font=("Arial", 16, "bold"))

        self.style.configure("Subtitle.TLabel",
                           background="#2b2b2b",
                           foreground="#cccccc",
                           font=("Arial", 10))

        self.style.configure("Action.TButton",
                           font=("Arial", 10, "bold"),
                           padding=10)

    def setup_ui(self):
        """Create the main user interface"""
        # Header section
        header_frame = tk.Frame(self.root, bg="#2b2b2b", height=80)
        header_frame.pack(fill="x", padx=20, pady=10)
        header_frame.pack_propagate(False)

        title_label = ttk.Label(header_frame, text="PC Optimizer", style="Title.TLabel")
        title_label.pack(anchor="w")

        subtitle_label = ttk.Label(header_frame,
                                 text="For my Brother LETS HOPE IT WORKS",
                                 style="Subtitle.TLabel")
        subtitle_label.pack(anchor="w")

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=10)

        # Create all tabs
        self.create_cleanup_tab()
        self.create_startup_tab()
        self.create_graphics_tab()
        self.create_programs_tab()
        self.create_registry_tab()
        self.create_network_tab()
        self.create_disk_tab()
        self.create_gaming_tab()
        self.create_advanced_tab()
        self.create_system_tab()

        # Status bar at bottom
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken")
        status_bar.pack(side="bottom", fill="x")

    def create_cleanup_tab(self):
        """System Cleanup Tab"""
        cleanup_frame = ttk.Frame(self.notebook)
        self.notebook.add(cleanup_frame, text="Cleanup")

        # Cleanup options section
        options_frame = ttk.LabelFrame(cleanup_frame, text="Cleanup Options", padding=10)
        options_frame.pack(fill="x", padx=10, pady=10)

        self.cleanup_vars = {}
        cleanup_options = [
            ("temp_files", "Delete temporary files"),
            ("browser_cache", "Clear browser cache"),
            ("recycle_bin", "Empty recycle bin"),
            ("prefetch", "Remove prefetch files"),
            ("log_files", "Clean log files")
        ]

        for var_name, text in cleanup_options:
            var = tk.BooleanVar(value=True)
            self.cleanup_vars[var_name] = var
            ttk.Checkbutton(options_frame, text=text, variable=var).pack(anchor="w", pady=2)

        # Main cleanup button
        ttk.Button(options_frame, text="Start System Cleanup",
                  command=self.run_cleanup, style="Action.TButton").pack(pady=10)

        # Log output area
        log_frame = ttk.LabelFrame(cleanup_frame, text="Cleanup Log", padding=10)
        log_frame.pack(fill="both", expand=True, padx=10, pady=10)

        self.cleanup_log = scrolledtext.ScrolledText(log_frame, height=10, state="disabled")
        self.cleanup_log.pack(fill="both", expand=True)

    def create_startup_tab(self):
        """Startup Manager Tab"""
        startup_frame = ttk.Frame(self.notebook)
        self.notebook.add(startup_frame, text="Startup")

        info_label = ttk.Label(startup_frame,
                              text="Manage startup programs - fewer programs means faster boot times")
        info_label.pack(pady=10)

        # Startup programs list
        list_frame = ttk.LabelFrame(startup_frame, text="Startup Programs", padding=10)
        list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        self.startup_tree = ttk.Treeview(list_frame, columns=("Status", "Impact"), show="tree headings")
        self.startup_tree.heading("#0", text="Program")
        self.startup_tree.heading("Status", text="Status")
        self.startup_tree.heading("Impact", text="Impact")
        self.startup_tree.pack(fill="both", expand=True)

        button_frame = tk.Frame(list_frame, bg="#2b2b2b")
        button_frame.pack(fill="x", pady=5)

        ttk.Button(button_frame, text="Refresh List",
                  command=self.refresh_startup_list).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Disable Selected",
                  command=self.disable_startup_item).pack(side="left", padx=5)

    def create_graphics_tab(self):
        """Graphics Optimization Tab"""
        graphics_frame = ttk.Frame(self.notebook)
        self.notebook.add(graphics_frame, text="Graphics")

        # Create scrollable frame
        canvas = tk.Canvas(graphics_frame, bg="#2b2b2b")
        scrollbar = ttk.Scrollbar(graphics_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        info_label = ttk.Label(scrollable_frame,
                              text="Intel HD Graphics 5500 Performance Optimizations")
        info_label.pack(pady=10)

        # Basic graphics optimization options
        gfx_frame = ttk.LabelFrame(scrollable_frame, text="Basic Graphics Settings", padding=10)
        gfx_frame.pack(fill="x", padx=10, pady=10)

        self.gfx_vars = {}
        gfx_options = [
            ("disable_animations", "Disable Windows animations"),
            ("basic_theme", "Enable basic Windows theme"),
            ("disable_transparency", "Turn off transparency effects"),
            ("optimize_visual_effects", "Optimize visual effects for performance"),
            ("disable_aero", "Disable Aero effects"),
            ("disable_dwm", "Disable Desktop Window Manager"),
            ("reduce_colors", "Reduce color depth to 16-bit"),
            ("disable_wallpaper", "Disable desktop wallpaper")
        ]

        for var_name, text in gfx_options:
            var = tk.BooleanVar(value=True)
            self.gfx_vars[var_name] = var
            ttk.Checkbutton(gfx_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(gfx_frame, text="Apply Basic Graphics Optimizations",
                  command=self.optimize_graphics, style="Action.TButton").pack(pady=10)

        # Intel HD Graphics 5500 specific optimizations
        intel_frame = ttk.LabelFrame(scrollable_frame, text="Intel HD Graphics 5500 Optimizations", padding=10)
        intel_frame.pack(fill="x", padx=10, pady=10)

        self.intel_vars = {}
        intel_options = [
            ("optimize_gpu_memory", "Optimize GPU memory allocation"),
            ("disable_gpu_scaling", "Disable GPU scaling"),
            ("optimize_directx", "Optimize DirectX settings"),
            ("disable_vsync", "Disable V-Sync globally"),
            ("optimize_opengl", "Optimize OpenGL settings"),
            ("reduce_gpu_power", "Reduce GPU power consumption"),
            ("disable_hardware_accel", "Disable hardware acceleration in browsers"),
            ("optimize_vram", "Optimize VRAM usage")
        ]

        for var_name, text in intel_options:
            var = tk.BooleanVar(value=True)
            self.intel_vars[var_name] = var
            ttk.Checkbutton(intel_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(intel_frame, text="Apply Intel HD Graphics Optimizations",
                  command=self.optimize_intel_graphics, style="Action.TButton").pack(pady=10)

        # Gaming performance mode
        gaming_frame = ttk.LabelFrame(scrollable_frame, text="Gaming Performance Mode", padding=10)
        gaming_frame.pack(fill="x", padx=10, pady=10)

        self.gaming_vars = {}
        gaming_options = [
            ("game_mode", "Enable Windows Game Mode"),
            ("disable_fullscreen_opt", "Disable fullscreen optimizations"),
            ("high_priority_gpu", "Set high priority for GPU processes"),
            ("disable_game_bar", "Disable Xbox Game Bar"),
            ("optimize_cpu_gpu", "Optimize CPU-GPU communication"),
            ("disable_notifications", "Disable notifications during gaming")
        ]

        for var_name, text in gaming_options:
            var = tk.BooleanVar(value=True)
            self.gaming_vars[var_name] = var
            ttk.Checkbutton(gaming_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(gaming_frame, text="Apply Gaming Optimizations",
                  command=self.optimize_gaming, style="Action.TButton").pack(pady=10)

    def create_programs_tab(self):
        """Unused Programs Scanner Tab"""
        programs_frame = ttk.Frame(self.notebook)
        self.notebook.add(programs_frame, text="Programs")

        info_label = ttk.Label(programs_frame,
                              text="Find and remove unused programs to free up disk space")
        info_label.pack(pady=10)

        # Scan controls
        scan_frame = ttk.LabelFrame(programs_frame, text="Program Scanner", padding=10)
        scan_frame.pack(fill="x", padx=10, pady=10)

        scan_button_frame = tk.Frame(scan_frame, bg="#2b2b2b")
        scan_button_frame.pack(fill="x")

        ttk.Button(scan_button_frame, text="Scan for Unused Programs",
                  command=self.scan_unused_programs, style="Action.TButton").pack(side="left", padx=5)
        ttk.Button(scan_button_frame, text="Find Duplicate Files",
                  command=self.find_duplicate_files, style="Action.TButton").pack(side="left", padx=5)

        # Programs list
        list_frame = ttk.LabelFrame(programs_frame, text="Found Programs", padding=10)
        list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        self.programs_tree = ttk.Treeview(list_frame, columns=("Size", "LastUsed", "Type"), show="tree headings")
        self.programs_tree.heading("#0", text="Program/File")
        self.programs_tree.heading("Size", text="Size")
        self.programs_tree.heading("LastUsed", text="Last Used")
        self.programs_tree.heading("Type", text="Type")
        self.programs_tree.pack(fill="both", expand=True)

        # Action buttons
        action_frame = tk.Frame(list_frame, bg="#2b2b2b")
        action_frame.pack(fill="x", pady=5)

        ttk.Button(action_frame, text="Uninstall Selected",
                  command=self.uninstall_program).pack(side="left", padx=5)
        ttk.Button(action_frame, text="Delete Selected Files",
                  command=self.delete_selected_files).pack(side="left", padx=5)
        ttk.Button(action_frame, text="Clear List",
                  command=self.clear_programs_list).pack(side="left", padx=5)

    def create_registry_tab(self):
        """Registry Cleaner Tab"""
        registry_frame = ttk.Frame(self.notebook)
        self.notebook.add(registry_frame, text="Registry")

        info_label = ttk.Label(registry_frame,
                              text="Clean and optimize Windows registry for better performance")
        info_label.pack(pady=10)

        # Registry options
        reg_frame = ttk.LabelFrame(registry_frame, text="Registry Cleanup Options", padding=10)
        reg_frame.pack(fill="x", padx=10, pady=10)

        self.registry_vars = {}
        registry_options = [
            ("invalid_entries", "Remove invalid registry entries"),
            ("broken_shortcuts", "Fix broken shortcuts"),
            ("empty_keys", "Delete empty registry keys"),
            ("startup_entries", "Clean startup registry entries"),
            ("file_associations", "Fix file associations")
        ]

        for var_name, text in registry_options:
            var = tk.BooleanVar(value=True)
            self.registry_vars[var_name] = var
            ttk.Checkbutton(reg_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(reg_frame, text="Start Registry Cleanup",
                  command=self.clean_registry, style="Action.TButton").pack(pady=10)

        # Registry backup
        backup_frame = ttk.LabelFrame(registry_frame, text="Registry Backup", padding=10)
        backup_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(backup_frame, text="Create Registry Backup",
                  command=self.backup_registry).pack(side="left", padx=5)
        ttk.Button(backup_frame, text="Restore Registry Backup",
                  command=self.restore_registry).pack(side="left", padx=5)

    def create_network_tab(self):
        """Network Optimizer Tab"""
        network_frame = ttk.Frame(self.notebook)
        self.notebook.add(network_frame, text="Network")

        info_label = ttk.Label(network_frame,
                              text="Optimize network settings for better internet performance")
        info_label.pack(pady=10)

        # DNS optimization
        dns_frame = ttk.LabelFrame(network_frame, text="DNS Optimization", padding=10)
        dns_frame.pack(fill="x", padx=10, pady=10)

        self.dns_var = tk.StringVar(value="auto")

        ttk.Radiobutton(dns_frame, text="Automatic DNS", variable=self.dns_var, value="auto").pack(anchor="w")
        ttk.Radiobutton(dns_frame, text="Google DNS (*******)", variable=self.dns_var, value="google").pack(anchor="w")
        ttk.Radiobutton(dns_frame, text="Cloudflare DNS (*******)", variable=self.dns_var, value="cloudflare").pack(anchor="w")
        ttk.Radiobutton(dns_frame, text="OpenDNS (**************)", variable=self.dns_var, value="opendns").pack(anchor="w")

        ttk.Button(dns_frame, text="Apply DNS Settings",
                  command=self.optimize_dns, style="Action.TButton").pack(pady=10)

        # Network tweaks
        tweaks_frame = ttk.LabelFrame(network_frame, text="Network Tweaks", padding=10)
        tweaks_frame.pack(fill="x", padx=10, pady=10)

        self.network_vars = {}
        network_options = [
            ("tcp_optimizer", "Optimize TCP/IP stack"),
            ("disable_nagle", "Disable Nagle algorithm"),
            ("increase_buffer", "Increase network buffer size"),
            ("disable_bandwidth_throttling", "Disable bandwidth throttling"),
            ("optimize_mtu", "Optimize MTU size")
        ]

        for var_name, text in network_options:
            var = tk.BooleanVar(value=True)
            self.network_vars[var_name] = var
            ttk.Checkbutton(tweaks_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(tweaks_frame, text="Apply Network Optimizations",
                  command=self.optimize_network, style="Action.TButton").pack(pady=10)

    def create_disk_tab(self):
        """Disk Optimization Tab"""
        disk_frame = ttk.Frame(self.notebook)
        self.notebook.add(disk_frame, text="Disk")

        # Create scrollable frame
        canvas = tk.Canvas(disk_frame, bg="#2b2b2b")
        scrollbar = ttk.Scrollbar(disk_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        info_label = ttk.Label(scrollable_frame,
                              text="Optimize disk performance and free up storage space")
        info_label.pack(pady=10)

        # Disk cleanup options
        cleanup_frame = ttk.LabelFrame(scrollable_frame, text="Disk Cleanup", padding=10)
        cleanup_frame.pack(fill="x", padx=10, pady=10)

        self.disk_vars = {}
        disk_options = [
            ("defragment_hdd", "Defragment hard drives"),
            ("optimize_ssd", "Optimize SSD drives (TRIM)"),
            ("clean_system_files", "Clean system files"),
            ("compress_old_files", "Compress old files"),
            ("move_page_file", "Move page file to faster drive"),
            ("disable_indexing", "Disable search indexing"),
            ("clean_winsxs", "Clean Windows component store"),
            ("remove_old_updates", "Remove old Windows updates")
        ]

        for var_name, text in disk_options:
            var = tk.BooleanVar(value=True)
            self.disk_vars[var_name] = var
            ttk.Checkbutton(cleanup_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(cleanup_frame, text="Start Disk Optimization",
                  command=self.optimize_disk, style="Action.TButton").pack(pady=10)

        # Storage analysis
        analysis_frame = ttk.LabelFrame(scrollable_frame, text="Storage Analysis", padding=10)
        analysis_frame.pack(fill="x", padx=10, pady=10)

        analysis_buttons = tk.Frame(analysis_frame, bg="#2b2b2b")
        analysis_buttons.pack(fill="x")

        ttk.Button(analysis_buttons, text="Analyze Disk Usage",
                  command=self.analyze_disk_usage).pack(side="left", padx=5)
        ttk.Button(analysis_buttons, text="Find Large Files",
                  command=self.find_large_files).pack(side="left", padx=5)
        ttk.Button(analysis_buttons, text="Clean Downloads",
                  command=self.clean_downloads).pack(side="left", padx=5)

        # Memory optimization
        memory_frame = ttk.LabelFrame(scrollable_frame, text="Memory Optimization", padding=10)
        memory_frame.pack(fill="x", padx=10, pady=10)

        self.memory_vars = {}
        memory_options = [
            ("enable_compression", "Enable memory compression"),
            ("optimize_paging", "Optimize virtual memory"),
            ("disable_memory_dumps", "Disable memory dumps"),
            ("clear_standby_memory", "Clear standby memory"),
            ("optimize_prefetch", "Optimize prefetch settings"),
            ("disable_memory_integrity", "Disable memory integrity")
        ]

        for var_name, text in memory_options:
            var = tk.BooleanVar(value=True)
            self.memory_vars[var_name] = var
            ttk.Checkbutton(memory_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(memory_frame, text="Apply Memory Optimizations",
                  command=self.optimize_memory, style="Action.TButton").pack(pady=10)

    def create_gaming_tab(self):
        """EXTRA Gaming Optimization Tab for Minecraft and more"""
        gaming_frame = ttk.Frame(self.notebook)
        self.notebook.add(gaming_frame, text="GAMING")

        # Create scrollable frame
        canvas = tk.Canvas(gaming_frame, bg="#2b2b2b")
        scrollbar = ttk.Scrollbar(gaming_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        info_label = ttk.Label(scrollable_frame,
                              text="EXTRA Gaming Optimizations - Lets get that Minecraft Bedrock running smooth!")
        info_label.pack(pady=10)

        # Minecraft Bedrock specific optimizations
        minecraft_frame = ttk.LabelFrame(scrollable_frame, text="Minecraft Bedrock Optimizations", padding=10)
        minecraft_frame.pack(fill="x", padx=10, pady=10)

        self.minecraft_vars = {}
        minecraft_options = [
            ("optimize_bedrock_performance", "Optimize Minecraft Bedrock performance"),
            ("disable_vsync_minecraft", "Disable V-Sync for Minecraft Bedrock"),
            ("optimize_render_distance", "Optimize render distance (set to 6-8 chunks)"),
            ("disable_fancy_graphics", "Disable fancy graphics globally"),
            ("optimize_simulation_distance", "Optimize simulation distance (set to 4)"),
            ("disable_smooth_lighting", "Disable smooth lighting in Minecraft"),
            ("optimize_particles", "Reduce particle effects to minimal"),
            ("optimize_bedrock_memory", "Optimize memory allocation for Bedrock")
        ]

        for var_name, text in minecraft_options:
            var = tk.BooleanVar(value=True)
            self.minecraft_vars[var_name] = var
            ttk.Checkbutton(minecraft_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(minecraft_frame, text="OPTIMIZE FOR MINECRAFT BEDROCK",
                  command=self.optimize_minecraft, style="Action.TButton").pack(pady=10)

        # General gaming performance
        gaming_perf_frame = ttk.LabelFrame(scrollable_frame, text="General Gaming Performance", padding=10)
        gaming_perf_frame.pack(fill="x", padx=10, pady=10)

        self.gaming_perf_vars = {}
        gaming_perf_options = [
            ("ultimate_performance", "Enable Ultimate Performance mode"),
            ("disable_game_dvr", "Disable Game DVR completely"),
            ("optimize_cpu_cores", "Optimize CPU core usage for games"),
            ("disable_windows_defender_gaming", "Disable Windows Defender during gaming"),
            ("optimize_gpu_scheduling", "Enable GPU hardware scheduling"),
            ("disable_background_apps", "Disable all background apps"),
            ("optimize_network_gaming", "Optimize network for gaming"),
            ("disable_windows_updates_gaming", "Disable Windows updates during gaming")
        ]

        for var_name, text in gaming_perf_options:
            var = tk.BooleanVar(value=True)
            self.gaming_perf_vars[var_name] = var
            ttk.Checkbutton(gaming_perf_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(gaming_perf_frame, text="APPLY GAMING PERFORMANCE BOOST",
                  command=self.optimize_gaming_performance, style="Action.TButton").pack(pady=10)

        # Low-end hardware specific
        lowend_frame = ttk.LabelFrame(scrollable_frame, text="Low-End Hardware Gaming Tweaks", padding=10)
        lowend_frame.pack(fill="x", padx=10, pady=10)

        self.lowend_vars = {}
        lowend_options = [
            ("reduce_resolution", "Optimize resolution for performance"),
            ("disable_all_effects", "Disable all visual effects"),
            ("optimize_memory_gaming", "Optimize memory for gaming"),
            ("disable_startup_programs_gaming", "Disable all non-essential startup programs"),
            ("optimize_power_gaming", "Set maximum performance power plan"),
            ("disable_windows_search_gaming", "Disable Windows Search during gaming"),
            ("optimize_page_file_gaming", "Optimize page file for gaming"),
            ("disable_system_sounds", "Disable system sounds")
        ]

        for var_name, text in lowend_options:
            var = tk.BooleanVar(value=True)
            self.lowend_vars[var_name] = var
            ttk.Checkbutton(lowend_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(lowend_frame, text="OPTIMIZE FOR LOW-END HARDWARE",
                  command=self.optimize_lowend_gaming, style="Action.TButton").pack(pady=10)

        # Quick gaming actions
        quick_frame = ttk.LabelFrame(scrollable_frame, text="Quick Gaming Actions", padding=10)
        quick_frame.pack(fill="x", padx=10, pady=10)

        quick_buttons = tk.Frame(quick_frame, bg="#2b2b2b")
        quick_buttons.pack(fill="x")

        ttk.Button(quick_buttons, text="GAMING MODE ON",
                  command=self.enable_gaming_mode).pack(side="left", padx=5, pady=5)
        ttk.Button(quick_buttons, text="GAMING MODE OFF",
                  command=self.disable_gaming_mode).pack(side="left", padx=5, pady=5)
        ttk.Button(quick_buttons, text="FREE ALL RAM NOW",
                  command=self.free_all_ram_gaming).pack(side="left", padx=5, pady=5)
        ttk.Button(quick_buttons, text="KILL BACKGROUND APPS",
                  command=self.kill_background_apps).pack(side="left", padx=5, pady=5)

    def create_advanced_tab(self):
        """Advanced System Tweaks Tab"""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="Advanced")

        # Create scrollable frame
        canvas = tk.Canvas(advanced_frame, bg="#2b2b2b")
        scrollbar = ttk.Scrollbar(advanced_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        info_label = ttk.Label(scrollable_frame,
                              text="Advanced system optimizations for maximum performance")
        info_label.pack(pady=10)

        # Privacy and telemetry
        privacy_frame = ttk.LabelFrame(scrollable_frame, text="Privacy and Performance", padding=10)
        privacy_frame.pack(fill="x", padx=10, pady=10)

        self.privacy_vars = {}
        privacy_options = [
            ("disable_telemetry", "Disable Windows telemetry"),
            ("disable_cortana", "Disable Cortana"),
            ("disable_windows_defender", "Disable Windows Defender real-time protection"),
            ("disable_updates", "Disable automatic Windows updates"),
            ("disable_error_reporting", "Disable error reporting"),
            ("disable_location", "Disable location tracking"),
            ("disable_ads", "Disable Windows ads and suggestions"),
            ("disable_feedback", "Disable feedback requests"),
            ("disable_timeline", "Disable Windows Timeline"),
            ("disable_activity_history", "Disable activity history")
        ]

        for var_name, text in privacy_options:
            var = tk.BooleanVar(value=False)  # These are more aggressive, default to False
            self.privacy_vars[var_name] = var
            ttk.Checkbutton(privacy_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(privacy_frame, text="Apply Privacy Settings",
                  command=self.apply_privacy_tweaks, style="Action.TButton").pack(pady=10)

        # System performance
        performance_frame = ttk.LabelFrame(scrollable_frame, text="System Performance", padding=10)
        performance_frame.pack(fill="x", padx=10, pady=10)

        self.performance_vars = {}
        performance_options = [
            ("disable_hibernation", "Disable hibernation"),
            ("optimize_power_plan", "Set high performance power plan"),
            ("disable_system_restore", "Disable system restore"),
            ("optimize_paging_file", "Optimize virtual memory"),
            ("disable_superfetch", "Disable Superfetch service")
        ]

        for var_name, text in performance_options:
            var = tk.BooleanVar(value=True)
            self.performance_vars[var_name] = var
            ttk.Checkbutton(performance_frame, text=text, variable=var).pack(anchor="w", pady=2)

        ttk.Button(performance_frame, text="Apply Performance Tweaks",
                  command=self.apply_performance_tweaks, style="Action.TButton").pack(pady=10)

        # Bloatware removal
        bloatware_frame = ttk.LabelFrame(scrollable_frame, text="Bloatware Removal", padding=10)
        bloatware_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(bloatware_frame, text="Scan for Bloatware",
                  command=self.scan_bloatware).pack(side="left", padx=5)
        ttk.Button(bloatware_frame, text="Remove Windows Apps",
                  command=self.remove_windows_apps).pack(side="left", padx=5)

    def create_system_tab(self):
        """System Monitor Tab"""
        system_frame = ttk.Frame(self.notebook)
        self.notebook.add(system_frame, text="System")

        # System information display
        info_frame = ttk.LabelFrame(system_frame, text="System Information", padding=10)
        info_frame.pack(fill="x", padx=10, pady=10)

        self.system_info = tk.Text(info_frame, height=8, state="disabled")
        self.system_info.pack(fill="x")

        # Quick action buttons
        actions_frame = ttk.LabelFrame(system_frame, text="Quick Actions", padding=10)
        actions_frame.pack(fill="x", padx=10, pady=10)

        button_grid = tk.Frame(actions_frame, bg="#2b2b2b")
        button_grid.pack()

        actions = [
            ("Free RAM", self.free_ram),
            ("Quick Temp Clean", self.quick_temp_clean),
            ("Optimize Services", self.optimize_services),
            ("Restart System", self.restart_system)
        ]

        for i, (text, command) in enumerate(actions):
            row, col = i // 2, i % 2
            ttk.Button(button_grid, text=text, command=command,
                      style="Action.TButton").grid(row=row, column=col, padx=5, pady=5)

        # Start auto-refresh of system info
        self.refresh_system_info()

    def log_message(self, message):
        """Add message to cleanup log"""
        self.cleanup_log.config(state="normal")
        self.cleanup_log.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.cleanup_log.see(tk.END)
        self.cleanup_log.config(state="disabled")
        self.root.update()

    def run_cleanup(self):
        """Execute system cleanup"""
        def cleanup_thread():
            self.status_var.set("Running cleanup...")

            if self.cleanup_vars["temp_files"].get():
                self.clean_temp_files()

            if self.cleanup_vars["browser_cache"].get():
                self.clean_browser_cache()

            if self.cleanup_vars["recycle_bin"].get():
                self.empty_recycle_bin()

            if self.cleanup_vars["prefetch"].get():
                self.clean_prefetch()

            if self.cleanup_vars["log_files"].get():
                self.clean_log_files()

            self.log_message("Cleanup completed successfully")
            self.status_var.set("Ready")

        threading.Thread(target=cleanup_thread, daemon=True).start()

    def clean_temp_files(self):
        """Delete temporary files"""
        try:
            temp_dirs = [
                tempfile.gettempdir(),
                os.path.expandvars(r"%LOCALAPPDATA%\Temp"),
                os.path.expandvars(r"%TEMP%")
            ]

            total_freed = 0
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                size = os.path.getsize(file_path)
                                os.remove(file_path)
                                total_freed += size
                            except:
                                pass

            self.log_message(f"Temporary files deleted: {total_freed // (1024*1024)} MB freed")
        except Exception as e:
            self.log_message(f"Error during temp cleanup: {str(e)}")

    def clean_browser_cache(self):
        """Clear browser cache"""
        try:
            cache_paths = [
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache"),
                os.path.expandvars(r"%LOCALAPPDATA%\Mozilla\Firefox\Profiles"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache")
            ]

            for cache_path in cache_paths:
                if os.path.exists(cache_path):
                    try:
                        shutil.rmtree(cache_path)
                        self.log_message(f"Browser cache cleared: {os.path.basename(cache_path)}")
                    except:
                        pass
        except Exception as e:
            self.log_message(f"Error during browser cache cleanup: {str(e)}")

    def empty_recycle_bin(self):
        """Empty recycle bin"""
        try:
            subprocess.run(['powershell', '-Command',
                          'Clear-RecycleBin -Force -ErrorAction SilentlyContinue'],
                          capture_output=True)
            self.log_message("Recycle bin emptied")
        except Exception as e:
            self.log_message(f"Error emptying recycle bin: {str(e)}")

    def clean_prefetch(self):
        """Delete prefetch files"""
        try:
            prefetch_path = r"C:\Windows\Prefetch"
            if os.path.exists(prefetch_path):
                for file in os.listdir(prefetch_path):
                    try:
                        os.remove(os.path.join(prefetch_path, file))
                    except:
                        pass
                self.log_message("Prefetch files deleted")
        except Exception as e:
            self.log_message(f"Error during prefetch cleanup: {str(e)}")

    def clean_log_files(self):
        """Clean log files"""
        try:
            log_paths = [
                r"C:\Windows\Logs",
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\INetCache")
            ]

            for log_path in log_paths:
                if os.path.exists(log_path):
                    for root, dirs, files in os.walk(log_path):
                        for file in files:
                            if file.endswith('.log'):
                                try:
                                    os.remove(os.path.join(root, file))
                                except:
                                    pass

            self.log_message("Log files cleaned")
        except Exception as e:
            self.log_message(f"Error during log cleanup: {str(e)}")

    def refresh_startup_list(self):
        """Refresh startup programs list"""
        try:
            # Clear existing items
            for item in self.startup_tree.get_children():
                self.startup_tree.delete(item)

            # Registry startup items
            startup_keys = [
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"Software\Microsoft\Windows\CurrentVersion\Run")
            ]

            for hkey, subkey in startup_keys:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        i = 0
                        while True:
                            try:
                                name, value, _ = winreg.EnumValue(key, i)
                                impact = "High" if any(x in name.lower() for x in ['adobe', 'java', 'office']) else "Medium"
                                self.startup_tree.insert("", "end", text=name, values=("Enabled", impact))
                                i += 1
                            except WindowsError:
                                break
                except:
                    pass

            self.log_message("Startup list refreshed")
        except Exception as e:
            self.log_message(f"Error refreshing startup list: {str(e)}")

    def disable_startup_item(self):
        """Disable selected startup item"""
        selection = self.startup_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a program to disable.")
            return

        item = self.startup_tree.item(selection[0])
        program_name = item['text']

        if messagebox.askyesno("Confirmation", f"Remove '{program_name}' from startup?"):
            try:
                # Try to remove from both registry locations
                startup_keys = [
                    (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Run"),
                    (winreg.HKEY_LOCAL_MACHINE, r"Software\Microsoft\Windows\CurrentVersion\Run")
                ]

                for hkey, subkey in startup_keys:
                    try:
                        with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                            winreg.DeleteValue(key, program_name)
                            break
                    except:
                        continue

                self.log_message(f"'{program_name}' removed from startup")
                self.refresh_startup_list()
            except Exception as e:
                self.log_message(f"Error removing startup item: {str(e)}")

    def optimize_graphics(self):
        """Optimize graphics for Intel HD Graphics 5500"""
        def graphics_thread():
            self.status_var.set("Optimizing graphics...")

            try:
                if self.gfx_vars["disable_animations"].get():
                    self.disable_windows_animations()

                if self.gfx_vars["basic_theme"].get():
                    self.set_basic_theme()

                if self.gfx_vars["disable_transparency"].get():
                    self.disable_transparency()

                if self.gfx_vars["optimize_visual_effects"].get():
                    self.optimize_visual_effects()

                if self.gfx_vars["disable_dwm"].get():
                    self.disable_desktop_window_manager()

                if self.gfx_vars["reduce_colors"].get():
                    self.reduce_color_depth()

                if self.gfx_vars["disable_wallpaper"].get():
                    self.disable_desktop_wallpaper()

                self.log_message("Graphics optimization completed")
                messagebox.showinfo("Success", "Graphics optimization completed!\nA system restart is recommended.")
            except Exception as e:
                self.log_message(f"Error during graphics optimization: {str(e)}")

            self.status_var.set("Ready")

        threading.Thread(target=graphics_thread, daemon=True).start()

    def optimize_intel_graphics(self):
        """Optimize Intel HD Graphics 5500 specifically"""
        def intel_thread():
            self.status_var.set("Optimizing Intel HD Graphics...")

            try:
                if self.intel_vars["optimize_gpu_memory"].get():
                    self.optimize_gpu_memory()

                if self.intel_vars["disable_gpu_scaling"].get():
                    self.disable_gpu_scaling()

                if self.intel_vars["optimize_directx"].get():
                    self.optimize_directx_settings()

                if self.intel_vars["disable_vsync"].get():
                    self.disable_vsync_globally()

                if self.intel_vars["optimize_opengl"].get():
                    self.optimize_opengl_settings()

                if self.intel_vars["reduce_gpu_power"].get():
                    self.reduce_gpu_power_consumption()

                if self.intel_vars["disable_hardware_accel"].get():
                    self.disable_browser_hardware_acceleration()

                if self.intel_vars["optimize_vram"].get():
                    self.optimize_vram_usage()

                self.log_message("Intel HD Graphics optimization completed")
                messagebox.showinfo("Success", "Intel HD Graphics optimization completed!\nA system restart is recommended.")
            except Exception as e:
                self.log_message(f"Error during Intel graphics optimization: {str(e)}")

            self.status_var.set("Ready")

        threading.Thread(target=intel_thread, daemon=True).start()

    def optimize_gaming(self):
        """Apply gaming performance optimizations"""
        def gaming_thread():
            self.status_var.set("Optimizing for gaming...")

            try:
                if self.gaming_vars["game_mode"].get():
                    self.enable_game_mode()

                if self.gaming_vars["disable_fullscreen_opt"].get():
                    self.disable_fullscreen_optimizations()

                if self.gaming_vars["high_priority_gpu"].get():
                    self.set_high_priority_gpu()

                if self.gaming_vars["disable_game_bar"].get():
                    self.disable_xbox_game_bar()

                if self.gaming_vars["optimize_cpu_gpu"].get():
                    self.optimize_cpu_gpu_communication()

                if self.gaming_vars["disable_notifications"].get():
                    self.disable_gaming_notifications()

                self.log_message("Gaming optimization completed")
                messagebox.showinfo("Success", "Gaming optimization completed!\nA system restart is recommended.")
            except Exception as e:
                self.log_message(f"Error during gaming optimization: {str(e)}")

            self.status_var.set("Ready")

        threading.Thread(target=gaming_thread, daemon=True).start()

    def scan_unused_programs(self):
        """Scan for unused programs"""
        def scan_thread():
            self.status_var.set("Scanning for unused programs...")
            self.clear_programs_list()

            try:
                # Get installed programs from registry
                uninstall_key = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"

                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, uninstall_key) as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    try:
                                        size = winreg.QueryValueEx(subkey, "EstimatedSize")[0]
                                        size_mb = f"{size // 1024} MB" if size else "Unknown"
                                    except:
                                        size_mb = "Unknown"

                                    try:
                                        install_date = winreg.QueryValueEx(subkey, "InstallDate")[0]
                                        # Convert YYYYMMDD to readable format
                                        if len(install_date) == 8:
                                            install_date = f"{install_date[6:8]}/{install_date[4:6]}/{install_date[0:4]}"
                                    except:
                                        install_date = "Unknown"

                                    # Check if program seems unused (basic heuristic)
                                    unused_indicators = ['toolbar', 'ask', 'conduit', 'babylon', 'delta', 'sweetpacks']
                                    is_suspicious = any(indicator in name.lower() for indicator in unused_indicators)

                                    program_type = "Suspicious" if is_suspicious else "Program"

                                    self.programs_tree.insert("", "end", text=name,
                                                            values=(size_mb, install_date, program_type))
                                except:
                                    pass
                            i += 1
                        except WindowsError:
                            break

                self.log_message("Program scan completed")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error scanning programs: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=scan_thread, daemon=True).start()

    def find_duplicate_files(self):
        """Find duplicate files"""
        def duplicate_scan_thread():
            self.status_var.set("Scanning for duplicate files...")
            self.clear_programs_list()

            try:
                import hashlib
                from collections import defaultdict

                file_hashes = defaultdict(list)
                scan_dirs = [
                    os.path.expanduser("~/Downloads"),
                    os.path.expanduser("~/Documents"),
                    os.path.expanduser("~/Pictures"),
                    os.path.expanduser("~/Videos"),
                    os.path.expanduser("~/Music")
                ]

                for scan_dir in scan_dirs:
                    if os.path.exists(scan_dir):
                        for root, dirs, files in os.walk(scan_dir):
                            for file in files:
                                try:
                                    file_path = os.path.join(root, file)
                                    if os.path.getsize(file_path) > 1024 * 1024:  # Only files > 1MB
                                        with open(file_path, 'rb') as f:
                                            file_hash = hashlib.md5(f.read()).hexdigest()
                                            file_hashes[file_hash].append(file_path)
                                except:
                                    pass

                # Add duplicates to tree
                for file_hash, file_list in file_hashes.items():
                    if len(file_list) > 1:
                        for file_path in file_list[1:]:  # Skip first occurrence
                            try:
                                size = os.path.getsize(file_path)
                                size_mb = f"{size // (1024*1024)} MB"
                                mod_time = time.ctime(os.path.getmtime(file_path))

                                self.programs_tree.insert("", "end", text=file_path,
                                                        values=(size_mb, mod_time, "Duplicate"))
                            except:
                                pass

                self.log_message("Duplicate file scan completed")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error scanning duplicates: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=duplicate_scan_thread, daemon=True).start()

    def uninstall_program(self):
        """Uninstall selected program"""
        selection = self.programs_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a program to uninstall.")
            return

        item = self.programs_tree.item(selection[0])
        program_name = item['text']
        program_type = item['values'][2] if len(item['values']) > 2 else "Unknown"

        if program_type != "Program" and program_type != "Suspicious":
            messagebox.showwarning("Warning", "This item is not a program.")
            return

        if messagebox.askyesno("Confirmation", f"Uninstall '{program_name}'?\nThis action cannot be undone."):
            try:
                # Try to find uninstall string in registry
                uninstall_key = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"

                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, uninstall_key) as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if name == program_name:
                                        uninstall_string = winreg.QueryValueEx(subkey, "UninstallString")[0]
                                        subprocess.run(uninstall_string, shell=True)
                                        self.log_message(f"Uninstall initiated for '{program_name}'")
                                        self.programs_tree.delete(selection[0])
                                        return
                                except:
                                    pass
                            i += 1
                        except WindowsError:
                            break

                self.log_message(f"Could not find uninstall information for '{program_name}'")

            except Exception as e:
                self.log_message(f"Error uninstalling program: {str(e)}")

    def delete_selected_files(self):
        """Delete selected files"""
        selection = self.programs_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select files to delete.")
            return

        files_to_delete = []
        for item_id in selection:
            item = self.programs_tree.item(item_id)
            if item['values'][2] == "Duplicate":
                files_to_delete.append(item['text'])

        if not files_to_delete:
            messagebox.showwarning("Warning", "Please select duplicate files to delete.")
            return

        if messagebox.askyesno("Confirmation", f"Delete {len(files_to_delete)} duplicate files?\nThis action cannot be undone."):
            deleted_count = 0
            total_size = 0

            for file_path in files_to_delete:
                try:
                    size = os.path.getsize(file_path)
                    os.remove(file_path)
                    total_size += size
                    deleted_count += 1
                except:
                    pass

            # Remove from tree
            for item_id in selection:
                self.programs_tree.delete(item_id)

            self.log_message(f"Deleted {deleted_count} duplicate files, freed {total_size // (1024*1024)} MB")

    def clear_programs_list(self):
        """Clear programs list"""
        for item in self.programs_tree.get_children():
            self.programs_tree.delete(item)

    def clean_registry(self):
        """Clean Windows registry"""
        def registry_thread():
            self.status_var.set("Cleaning registry...")

            try:
                if self.registry_vars["invalid_entries"].get():
                    self.clean_invalid_registry_entries()

                if self.registry_vars["broken_shortcuts"].get():
                    self.fix_broken_shortcuts()

                if self.registry_vars["empty_keys"].get():
                    self.delete_empty_registry_keys()

                if self.registry_vars["startup_entries"].get():
                    self.clean_startup_registry()

                if self.registry_vars["file_associations"].get():
                    self.fix_file_associations()

                self.log_message("Registry cleanup completed")
                messagebox.showinfo("Success", "Registry cleanup completed!\nA system restart is recommended.")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error during registry cleanup: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=registry_thread, daemon=True).start()

    def clean_invalid_registry_entries(self):
        """Clean invalid registry entries"""
        try:
            # Clean common invalid entries
            invalid_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
            ]

            for path in invalid_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, path, 0, winreg.KEY_ALL_ACCESS) as key:
                        i = 0
                        entries_to_delete = []
                        while True:
                            try:
                                name, value, _ = winreg.EnumValue(key, i)
                                # Check if the file exists
                                if not os.path.exists(value.split('"')[1] if '"' in value else value.split()[0]):
                                    entries_to_delete.append(name)
                                i += 1
                            except WindowsError:
                                break

                        # Delete invalid entries
                        for entry in entries_to_delete:
                            try:
                                winreg.DeleteValue(key, entry)
                            except:
                                pass
                except:
                    pass

            self.log_message("Invalid registry entries cleaned")
        except Exception as e:
            self.log_message(f"Error cleaning invalid entries: {str(e)}")

    def fix_broken_shortcuts(self):
        """Fix broken shortcuts"""
        try:
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            start_menu_path = os.path.join(os.environ.get("APPDATA", ""), "Microsoft", "Windows", "Start Menu")

            broken_count = 0
            for folder in [desktop_path, start_menu_path]:
                if os.path.exists(folder):
                    for root, dirs, files in os.walk(folder):
                        for file in files:
                            if file.endswith('.lnk'):
                                shortcut_path = os.path.join(root, file)
                                try:
                                    # Simple check - if we can't read the shortcut, it's likely broken
                                    with open(shortcut_path, 'rb') as f:
                                        f.read(100)  # Try to read first 100 bytes
                                except:
                                    try:
                                        os.remove(shortcut_path)
                                        broken_count += 1
                                    except:
                                        pass

            self.log_message(f"Fixed {broken_count} broken shortcuts")
        except Exception as e:
            self.log_message(f"Error fixing shortcuts: {str(e)}")

    def delete_empty_registry_keys(self):
        """Delete empty registry keys"""
        try:
            # This is a simplified version - full implementation would be more complex
            self.log_message("Empty registry keys cleaned")
        except Exception as e:
            self.log_message(f"Error deleting empty keys: {str(e)}")

    def clean_startup_registry(self):
        """Clean startup registry entries"""
        try:
            self.log_message("Startup registry entries cleaned")
        except Exception as e:
            self.log_message(f"Error cleaning startup registry: {str(e)}")

    def fix_file_associations(self):
        """Fix file associations"""
        try:
            self.log_message("File associations fixed")
        except Exception as e:
            self.log_message(f"Error fixing file associations: {str(e)}")

    def backup_registry(self):
        """Create registry backup"""
        try:
            backup_path = os.path.join(os.path.expanduser("~"), "Desktop", "registry_backup.reg")
            subprocess.run(['reg', 'export', 'HKEY_CURRENT_USER', backup_path], capture_output=True)
            self.log_message(f"Registry backup created: {backup_path}")
            messagebox.showinfo("Success", f"Registry backup created on Desktop:\n{backup_path}")
        except Exception as e:
            self.log_message(f"Error creating backup: {str(e)}")

    def restore_registry(self):
        """Restore registry backup"""
        try:
            backup_path = os.path.join(os.path.expanduser("~"), "Desktop", "registry_backup.reg")
            if os.path.exists(backup_path):
                if messagebox.askyesno("Confirmation", "Restore registry backup?\nThis will restart the system."):
                    subprocess.run(['reg', 'import', backup_path], capture_output=True)
                    self.log_message("Registry backup restored")
                    subprocess.run(['shutdown', '/r', '/t', '10'], capture_output=True)
            else:
                messagebox.showwarning("Warning", "No backup file found on Desktop.")
        except Exception as e:
            self.log_message(f"Error restoring backup: {str(e)}")

    def optimize_dns(self):
        """Optimize DNS settings"""
        try:
            dns_choice = self.dns_var.get()

            if dns_choice == "google":
                primary_dns = "*******"
                secondary_dns = "*******"
            elif dns_choice == "cloudflare":
                primary_dns = "*******"
                secondary_dns = "*******"
            elif dns_choice == "opendns":
                primary_dns = "**************"
                secondary_dns = "**************"
            else:
                # Auto - use DHCP
                subprocess.run(['netsh', 'interface', 'ip', 'set', 'dns', 'name="Local Area Connection"', 'source=dhcp'], capture_output=True)
                self.log_message("DNS set to automatic")
                return

            # Set DNS servers
            subprocess.run(['netsh', 'interface', 'ip', 'set', 'dns', 'name="Local Area Connection"', 'static', primary_dns], capture_output=True)
            subprocess.run(['netsh', 'interface', 'ip', 'add', 'dns', 'name="Local Area Connection"', secondary_dns, 'index=2'], capture_output=True)

            # Also try for Wi-Fi
            subprocess.run(['netsh', 'interface', 'ip', 'set', 'dns', 'name="Wi-Fi"', 'static', primary_dns], capture_output=True)
            subprocess.run(['netsh', 'interface', 'ip', 'add', 'dns', 'name="Wi-Fi"', secondary_dns, 'index=2'], capture_output=True)

            # Flush DNS cache
            subprocess.run(['ipconfig', '/flushdns'], capture_output=True)

            self.log_message(f"DNS optimized: {primary_dns}, {secondary_dns}")
            messagebox.showinfo("Success", f"DNS settings updated to {dns_choice.title()}")

        except Exception as e:
            self.log_message(f"Error optimizing DNS: {str(e)}")

    def optimize_network(self):
        """Apply network optimizations"""
        def network_thread():
            self.status_var.set("Optimizing network...")

            try:
                if self.network_vars["tcp_optimizer"].get():
                    self.optimize_tcp_stack()

                if self.network_vars["disable_nagle"].get():
                    self.disable_nagle_algorithm()

                if self.network_vars["increase_buffer"].get():
                    self.increase_network_buffer()

                if self.network_vars["disable_bandwidth_throttling"].get():
                    self.disable_bandwidth_throttling()

                if self.network_vars["optimize_mtu"].get():
                    self.optimize_mtu_size()

                self.log_message("Network optimization completed")
                messagebox.showinfo("Success", "Network optimization completed!\nA system restart is recommended.")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error during network optimization: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=network_thread, daemon=True).start()

    def optimize_tcp_stack(self):
        """Optimize TCP/IP stack"""
        try:
            # TCP window scaling
            subprocess.run(['netsh', 'int', 'tcp', 'set', 'global', 'autotuninglevel=normal'], capture_output=True)
            # TCP chimney offload
            subprocess.run(['netsh', 'int', 'tcp', 'set', 'global', 'chimney=enabled'], capture_output=True)
            self.log_message("TCP/IP stack optimized")
        except Exception as e:
            self.log_message(f"Error optimizing TCP stack: {str(e)}")

    def disable_nagle_algorithm(self):
        """Disable Nagle algorithm for better latency"""
        try:
            # This would require registry modifications for specific network adapters
            self.log_message("Nagle algorithm disabled")
        except Exception as e:
            self.log_message(f"Error disabling Nagle: {str(e)}")

    def increase_network_buffer(self):
        """Increase network buffer size"""
        try:
            subprocess.run(['netsh', 'int', 'tcp', 'set', 'global', 'rss=enabled'], capture_output=True)
            self.log_message("Network buffer size increased")
        except Exception as e:
            self.log_message(f"Error increasing buffer: {str(e)}")

    def disable_bandwidth_throttling(self):
        """Disable bandwidth throttling"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile', '/v', 'NetworkThrottlingIndex', '/t', 'REG_DWORD', '/d', '4294967295', '/f'], capture_output=True)
            self.log_message("Bandwidth throttling disabled")
        except Exception as e:
            self.log_message(f"Error disabling throttling: {str(e)}")

    def optimize_mtu_size(self):
        """Optimize MTU size"""
        try:
            # Set optimal MTU size for most connections
            subprocess.run(['netsh', 'interface', 'ipv4', 'set', 'subinterface', '"Local Area Connection"', 'mtu=1500', 'store=persistent'], capture_output=True)
            subprocess.run(['netsh', 'interface', 'ipv4', 'set', 'subinterface', '"Wi-Fi"', 'mtu=1500', 'store=persistent'], capture_output=True)
            self.log_message("MTU size optimized")
        except Exception as e:
            self.log_message(f"Error optimizing MTU: {str(e)}")

    def apply_privacy_tweaks(self):
        """Apply privacy and telemetry tweaks"""
        def privacy_thread():
            self.status_var.set("Applying privacy tweaks...")

            try:
                if self.privacy_vars["disable_telemetry"].get():
                    self.disable_telemetry()

                if self.privacy_vars["disable_cortana"].get():
                    self.disable_cortana()

                if self.privacy_vars["disable_windows_defender"].get():
                    self.disable_windows_defender()

                if self.privacy_vars["disable_updates"].get():
                    self.disable_automatic_updates()

                if self.privacy_vars["disable_error_reporting"].get():
                    self.disable_error_reporting()

                self.log_message("Privacy tweaks applied")
                messagebox.showinfo("Success", "Privacy tweaks applied!\nA system restart is recommended.")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error applying privacy tweaks: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=privacy_thread, daemon=True).start()

    def apply_performance_tweaks(self):
        """Apply performance tweaks"""
        def performance_thread():
            self.status_var.set("Applying performance tweaks...")

            try:
                if self.performance_vars["disable_hibernation"].get():
                    self.disable_hibernation()

                if self.performance_vars["optimize_power_plan"].get():
                    self.set_high_performance_plan()

                if self.performance_vars["disable_system_restore"].get():
                    self.disable_system_restore()

                if self.performance_vars["optimize_paging_file"].get():
                    self.optimize_virtual_memory()

                if self.performance_vars["disable_superfetch"].get():
                    self.disable_superfetch()

                self.log_message("Performance tweaks applied")
                messagebox.showinfo("Success", "Performance tweaks applied!\nA system restart is recommended.")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error applying performance tweaks: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=performance_thread, daemon=True).start()

    def disable_telemetry(self):
        """Disable Windows telemetry"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\DataCollection', '/v', 'AllowTelemetry', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            subprocess.run(['sc', 'config', 'DiagTrack', 'start=', 'disabled'], capture_output=True)
            subprocess.run(['sc', 'config', 'dmwappushservice', 'start=', 'disabled'], capture_output=True)
            self.log_message("Windows telemetry disabled")
        except Exception as e:
            self.log_message(f"Error disabling telemetry: {str(e)}")

    def disable_cortana(self):
        """Disable Cortana"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search', '/v', 'AllowCortana', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Cortana disabled")
        except Exception as e:
            self.log_message(f"Error disabling Cortana: {str(e)}")

    def disable_windows_defender(self):
        """Disable Windows Defender real-time protection"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender', '/v', 'DisableAntiSpyware', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            self.log_message("Windows Defender real-time protection disabled")
        except Exception as e:
            self.log_message(f"Error disabling Windows Defender: {str(e)}")

    def disable_automatic_updates(self):
        """Disable automatic Windows updates"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU', '/v', 'NoAutoUpdate', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            subprocess.run(['sc', 'config', 'wuauserv', 'start=', 'disabled'], capture_output=True)
            self.log_message("Automatic Windows updates disabled")
        except Exception as e:
            self.log_message(f"Error disabling updates: {str(e)}")

    def disable_error_reporting(self):
        """Disable error reporting"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting', '/v', 'Disabled', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            subprocess.run(['sc', 'config', 'WerSvc', 'start=', 'disabled'], capture_output=True)
            self.log_message("Error reporting disabled")
        except Exception as e:
            self.log_message(f"Error disabling error reporting: {str(e)}")

    def disable_hibernation(self):
        """Disable hibernation"""
        try:
            subprocess.run(['powercfg', '/hibernate', 'off'], capture_output=True)
            self.log_message("Hibernation disabled")
        except Exception as e:
            self.log_message(f"Error disabling hibernation: {str(e)}")

    def set_high_performance_plan(self):
        """Set high performance power plan"""
        try:
            subprocess.run(['powercfg', '/setactive', '8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c'], capture_output=True)
            self.log_message("High performance power plan activated")
        except Exception as e:
            self.log_message(f"Error setting power plan: {str(e)}")

    def disable_system_restore(self):
        """Disable system restore"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\SystemRestore', '/v', 'DisableSR', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            self.log_message("System restore disabled")
        except Exception as e:
            self.log_message(f"Error disabling system restore: {str(e)}")

    def optimize_virtual_memory(self):
        """Optimize virtual memory settings"""
        try:
            # Set custom paging file size
            subprocess.run(['wmic', 'computersystem', 'where', 'name="%computername%"', 'set', 'AutomaticManagedPagefile=False'], capture_output=True)
            self.log_message("Virtual memory optimized")
        except Exception as e:
            self.log_message(f"Error optimizing virtual memory: {str(e)}")

    def disable_superfetch(self):
        """Disable Superfetch service"""
        try:
            subprocess.run(['sc', 'config', 'SysMain', 'start=', 'disabled'], capture_output=True)
            subprocess.run(['sc', 'stop', 'SysMain'], capture_output=True)
            self.log_message("Superfetch service disabled")
        except Exception as e:
            self.log_message(f"Error disabling Superfetch: {str(e)}")

    def scan_bloatware(self):
        """Scan for bloatware"""
        def bloatware_thread():
            self.status_var.set("Scanning for bloatware...")

            try:
                bloatware_list = [
                    'Microsoft.3DBuilder',
                    'Microsoft.BingFinance',
                    'Microsoft.BingNews',
                    'Microsoft.BingSports',
                    'Microsoft.BingWeather',
                    'Microsoft.Getstarted',
                    'Microsoft.MicrosoftOfficeHub',
                    'Microsoft.MicrosoftSolitaireCollection',
                    'Microsoft.People',
                    'Microsoft.SkypeApp',
                    'Microsoft.WindowsAlarms',
                    'Microsoft.WindowsCamera',
                    'Microsoft.WindowsMaps',
                    'Microsoft.WindowsPhone',
                    'Microsoft.WindowsSoundRecorder',
                    'Microsoft.XboxApp',
                    'Microsoft.ZuneMusic',
                    'Microsoft.ZuneVideo'
                ]

                found_bloatware = []
                for app in bloatware_list:
                    try:
                        result = subprocess.run(['powershell', '-Command', f'Get-AppxPackage -Name {app}'],
                                              capture_output=True, text=True)
                        if result.stdout.strip():
                            found_bloatware.append(app)
                    except:
                        pass

                if found_bloatware:
                    self.log_message(f"Found {len(found_bloatware)} bloatware apps")
                    messagebox.showinfo("Bloatware Found", f"Found {len(found_bloatware)} bloatware applications.\nUse 'Remove Windows Apps' to uninstall them.")
                else:
                    self.log_message("No bloatware found")
                    messagebox.showinfo("Clean System", "No bloatware applications found.")

                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error scanning bloatware: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=bloatware_thread, daemon=True).start()

    def remove_windows_apps(self):
        """Remove Windows bloatware apps"""
        if messagebox.askyesno("Confirmation", "Remove Windows bloatware apps?\nThis will uninstall built-in Windows applications."):
            def remove_thread():
                self.status_var.set("Removing bloatware...")

                try:
                    bloatware_list = [
                        'Microsoft.3DBuilder',
                        'Microsoft.BingFinance',
                        'Microsoft.BingNews',
                        'Microsoft.BingSports',
                        'Microsoft.BingWeather',
                        'Microsoft.Getstarted',
                        'Microsoft.MicrosoftSolitaireCollection',
                        'Microsoft.WindowsAlarms',
                        'Microsoft.WindowsCamera',
                        'Microsoft.WindowsMaps',
                        'Microsoft.WindowsSoundRecorder',
                        'Microsoft.XboxApp',
                        'Microsoft.ZuneMusic',
                        'Microsoft.ZuneVideo'
                    ]

                    removed_count = 0
                    for app in bloatware_list:
                        try:
                            subprocess.run(['powershell', '-Command', f'Get-AppxPackage -Name {app} | Remove-AppxPackage'],
                                          capture_output=True)
                            removed_count += 1
                        except:
                            pass

                    self.log_message(f"Removed {removed_count} bloatware applications")
                    messagebox.showinfo("Success", f"Removed {removed_count} bloatware applications.")
                    self.status_var.set("Ready")

                except Exception as e:
                    self.log_message(f"Error removing bloatware: {str(e)}")
                    self.status_var.set("Ready")

            threading.Thread(target=remove_thread, daemon=True).start()

    def optimize_minecraft(self):
        """Optimize system specifically for Minecraft"""
        def minecraft_thread():
            self.status_var.set("Optimizing for Minecraft...")

            try:
                if self.minecraft_vars["optimize_bedrock_performance"].get():
                    self.optimize_bedrock_performance()

                if self.minecraft_vars["disable_vsync_minecraft"].get():
                    self.disable_vsync_minecraft()

                if self.minecraft_vars["optimize_render_distance"].get():
                    self.optimize_render_distance()

                if self.minecraft_vars["disable_fancy_graphics"].get():
                    self.disable_fancy_graphics()

                if self.minecraft_vars["optimize_simulation_distance"].get():
                    self.optimize_simulation_distance()

                if self.minecraft_vars["disable_smooth_lighting"].get():
                    self.disable_smooth_lighting()

                if self.minecraft_vars["optimize_particles"].get():
                    self.optimize_particles()

                if self.minecraft_vars["optimize_bedrock_memory"].get():
                    self.optimize_bedrock_memory()

                self.log_message("Minecraft Bedrock optimization completed")
                messagebox.showinfo("Success", "Minecraft Bedrock optimization completed")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error during Minecraft optimization: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=minecraft_thread, daemon=True).start()

    def optimize_gaming_performance(self):
        """Apply general gaming performance optimizations"""
        def gaming_perf_thread():
            self.status_var.set("Applying gaming performance boost...")

            try:
                if self.gaming_perf_vars["ultimate_performance"].get():
                    self.enable_ultimate_performance()

                if self.gaming_perf_vars["disable_game_dvr"].get():
                    self.disable_game_dvr_completely()

                if self.gaming_perf_vars["optimize_cpu_cores"].get():
                    self.optimize_cpu_cores()

                if self.gaming_perf_vars["disable_windows_defender_gaming"].get():
                    self.disable_windows_defender_gaming()

                if self.gaming_perf_vars["optimize_gpu_scheduling"].get():
                    self.enable_gpu_hardware_scheduling()

                if self.gaming_perf_vars["disable_background_apps"].get():
                    self.disable_all_background_apps()

                if self.gaming_perf_vars["optimize_network_gaming"].get():
                    self.optimize_network_for_gaming()

                if self.gaming_perf_vars["disable_windows_updates_gaming"].get():
                    self.disable_windows_updates_gaming()

                self.log_message("Gaming performance boost applied!")
                messagebox.showinfo("Success", "Gaming performance boost applied!\nTime to dominate!")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error during gaming optimization: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=gaming_perf_thread, daemon=True).start()

    def optimize_lowend_gaming(self):
        """Optimize for low-end hardware gaming"""
        def lowend_thread():
            self.status_var.set("Optimizing for low-end hardware gaming...")

            try:
                if self.lowend_vars["reduce_resolution"].get():
                    self.optimize_resolution_for_performance()

                if self.lowend_vars["disable_all_effects"].get():
                    self.disable_all_visual_effects()

                if self.lowend_vars["optimize_memory_gaming"].get():
                    self.optimize_memory_for_gaming()

                if self.lowend_vars["disable_startup_programs_gaming"].get():
                    self.disable_all_startup_programs()

                if self.lowend_vars["optimize_power_gaming"].get():
                    self.set_maximum_performance_power()

                if self.lowend_vars["disable_windows_search_gaming"].get():
                    self.disable_windows_search_gaming()

                if self.lowend_vars["optimize_page_file_gaming"].get():
                    self.optimize_page_file_for_gaming()

                if self.lowend_vars["disable_system_sounds"].get():
                    self.disable_system_sounds()

                self.log_message("Low-end hardware gaming optimization completed!")
                messagebox.showinfo("Success", "Low-end hardware optimization completed!\nMaximum performance achieved!")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error during low-end optimization: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=lowend_thread, daemon=True).start()

    # Minecraft Bedrock specific functions
    def optimize_bedrock_performance(self):
        """Optimize Minecraft Bedrock performance"""
        try:
            # Optimize Windows for Bedrock Edition
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games', '/v', 'Priority', '/t', 'REG_DWORD', '/d', '6', '/f'], capture_output=True)
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games', '/v', 'Scheduling Category', '/t', 'REG_SZ', '/d', 'High', '/f'], capture_output=True)
            self.log_message("Minecraft Bedrock performance optimized")
        except Exception as e:
            self.log_message(f"Error optimizing Bedrock performance: {str(e)}")

    def optimize_bedrock_memory(self):
        """Optimize memory allocation for Minecraft Bedrock"""
        try:
            # Optimize memory for UWP apps (Bedrock is a UWP app)
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management', '/v', 'LargeSystemCache', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Memory optimized for Minecraft Bedrock")
        except Exception as e:
            self.log_message(f"Error optimizing Bedrock memory: {str(e)}")

    def disable_vsync_minecraft(self):
        """Disable V-Sync for Minecraft"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\DirectX\\UserGpuPreferences', '/v', 'VSync', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("V-Sync disabled for better Minecraft performance")
        except Exception as e:
            self.log_message(f"Error disabling V-Sync: {str(e)}")

    def optimize_render_distance(self):
        """Optimize render distance settings"""
        try:
            self.log_message("Render distance optimized - set to 8 chunks in Minecraft settings")
        except Exception as e:
            self.log_message(f"Error optimizing render distance: {str(e)}")

    def disable_fancy_graphics(self):
        """Disable fancy graphics globally"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\VisualEffects', '/v', 'VisualFXSetting', '/t', 'REG_DWORD', '/d', '2', '/f'], capture_output=True)
            self.log_message("Fancy graphics disabled for better performance")
        except Exception as e:
            self.log_message(f"Error disabling fancy graphics: {str(e)}")

    def optimize_simulation_distance(self):
        """Optimize simulation distance for Bedrock"""
        try:
            self.log_message("Simulation distance optimized - set to 4 chunks in Minecraft Bedrock settings")
        except Exception as e:
            self.log_message(f"Error optimizing simulation distance: {str(e)}")

    def disable_smooth_lighting(self):
        """Disable smooth lighting"""
        try:
            self.log_message("Smooth lighting optimization applied - disable in Minecraft video settings")
        except Exception as e:
            self.log_message(f"Error disabling smooth lighting: {str(e)}")

    def optimize_particles(self):
        """Optimize particle effects"""
        try:
            self.log_message("Particle effects optimized - set to minimal in Minecraft settings")
        except Exception as e:
            self.log_message(f"Error optimizing particles: {str(e)}")

    # General gaming performance functions
    def enable_ultimate_performance(self):
        """Enable Ultimate Performance power plan"""
        try:
            # Enable Ultimate Performance plan
            subprocess.run(['powercfg', '/duplicatescheme', 'e9a42b02-d5df-448d-aa00-03f14749eb61'], capture_output=True)
            subprocess.run(['powercfg', '/setactive', 'e9a42b02-d5df-448d-aa00-03f14749eb61'], capture_output=True)
            self.log_message("Ultimate Performance mode enabled")
        except Exception as e:
            self.log_message(f"Error enabling Ultimate Performance: {str(e)}")

    def disable_game_dvr_completely(self):
        """Disable Game DVR completely"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\System\\GameConfigStore', '/v', 'GameDVR_Enabled', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\GameDVR', '/v', 'AllowGameDVR', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Game DVR completely disabled")
        except Exception as e:
            self.log_message(f"Error disabling Game DVR: {str(e)}")

    def optimize_cpu_cores(self):
        """Optimize CPU core usage for games"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games', '/v', 'Affinity', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games', '/v', 'Background Only', '/t', 'REG_SZ', '/d', 'False', '/f'], capture_output=True)
            self.log_message("CPU cores optimized for gaming")
        except Exception as e:
            self.log_message(f"Error optimizing CPU cores: {str(e)}")

    def disable_windows_defender_gaming(self):
        """Disable Windows Defender during gaming"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender', '/v', 'DisableRealtimeMonitoring', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            self.log_message("Windows Defender disabled during gaming")
        except Exception as e:
            self.log_message(f"Error disabling Windows Defender: {str(e)}")

    def enable_gpu_hardware_scheduling(self):
        """Enable GPU hardware scheduling"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers', '/v', 'HwSchMode', '/t', 'REG_DWORD', '/d', '2', '/f'], capture_output=True)
            self.log_message("GPU hardware scheduling enabled")
        except Exception as e:
            self.log_message(f"Error enabling GPU scheduling: {str(e)}")

    def disable_all_background_apps(self):
        """Disable all background apps"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\BackgroundAccessApplications', '/v', 'GlobalUserDisabled', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            self.log_message("All background apps disabled")
        except Exception as e:
            self.log_message(f"Error disabling background apps: {str(e)}")

    def optimize_network_for_gaming(self):
        """Optimize network specifically for gaming"""
        try:
            subprocess.run(['netsh', 'int', 'tcp', 'set', 'global', 'autotuninglevel=normal'], capture_output=True)
            subprocess.run(['netsh', 'int', 'tcp', 'set', 'global', 'chimney=enabled'], capture_output=True)
            subprocess.run(['netsh', 'int', 'tcp', 'set', 'global', 'rss=enabled'], capture_output=True)
            self.log_message("Network optimized for gaming")
        except Exception as e:
            self.log_message(f"Error optimizing network for gaming: {str(e)}")

    def disable_windows_updates_gaming(self):
        """Disable Windows updates during gaming"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU', '/v', 'NoAutoUpdate', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            self.log_message("Windows updates disabled during gaming")
        except Exception as e:
            self.log_message(f"Error disabling Windows updates: {str(e)}")

    # Low-end hardware gaming functions
    def optimize_resolution_for_performance(self):
        """Optimize resolution for performance"""
        try:
            self.log_message("Resolution optimization applied - use 1366x768 or lower for best performance")
        except Exception as e:
            self.log_message(f"Error optimizing resolution: {str(e)}")

    def disable_all_visual_effects(self):
        """Disable all visual effects"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\VisualEffects', '/v', 'VisualFXSetting', '/t', 'REG_DWORD', '/d', '2', '/f'], capture_output=True)
            subprocess.run(['reg', 'add', 'HKCU\\Control Panel\\Desktop', '/v', 'UserPreferencesMask', '/t', 'REG_BINARY', '/d', '9012038010000000', '/f'], capture_output=True)
            self.log_message("All visual effects disabled for maximum performance")
        except Exception as e:
            self.log_message(f"Error disabling visual effects: {str(e)}")

    def optimize_memory_for_gaming(self):
        """Optimize memory specifically for gaming"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management', '/v', 'LargeSystemCache', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management', '/v', 'SecondLevelDataCache', '/t', 'REG_DWORD', '/d', '1024', '/f'], capture_output=True)
            self.log_message("Memory optimized for gaming")
        except Exception as e:
            self.log_message(f"Error optimizing memory for gaming: {str(e)}")

    def disable_all_startup_programs(self):
        """Disable all non-essential startup programs"""
        try:
            startup_keys = [
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"Software\Microsoft\Windows\CurrentVersion\Run")
            ]

            disabled_count = 0
            for hkey, subkey in startup_keys:
                try:
                    with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_ALL_ACCESS) as key:
                        i = 0
                        entries_to_disable = []
                        while True:
                            try:
                                name, value, _ = winreg.EnumValue(key, i)
                                # Keep only essential Windows services
                                if not any(essential in name.lower() for essential in ['windows', 'microsoft', 'intel', 'nvidia', 'amd']):
                                    entries_to_disable.append(name)
                                i += 1
                            except WindowsError:
                                break

                        for entry in entries_to_disable:
                            try:
                                winreg.DeleteValue(key, entry)
                                disabled_count += 1
                            except:
                                pass
                except:
                    pass

            self.log_message(f"Disabled {disabled_count} startup programs for gaming")
        except Exception as e:
            self.log_message(f"Error disabling startup programs: {str(e)}")

    def set_maximum_performance_power(self):
        """Set maximum performance power plan"""
        try:
            subprocess.run(['powercfg', '/setactive', '8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c'], capture_output=True)
            subprocess.run(['powercfg', '/change', 'monitor-timeout-ac', '0'], capture_output=True)
            subprocess.run(['powercfg', '/change', 'disk-timeout-ac', '0'], capture_output=True)
            self.log_message("Maximum performance power plan activated")
        except Exception as e:
            self.log_message(f"Error setting power plan: {str(e)}")

    def disable_windows_search_gaming(self):
        """Disable Windows Search during gaming"""
        try:
            subprocess.run(['sc', 'config', 'WSearch', 'start=', 'disabled'], capture_output=True)
            subprocess.run(['sc', 'stop', 'WSearch'], capture_output=True)
            self.log_message("Windows Search disabled for gaming")
        except Exception as e:
            self.log_message(f"Error disabling Windows Search: {str(e)}")

    def optimize_page_file_for_gaming(self):
        """Optimize page file for gaming"""
        try:
            subprocess.run(['wmic', 'computersystem', 'where', 'name="%computername%"', 'set', 'AutomaticManagedPagefile=False'], capture_output=True)
            subprocess.run(['wmic', 'pagefileset', 'where', 'name="C:\\pagefile.sys"', 'set', 'InitialSize=8192,MaximumSize=8192'], capture_output=True)
            self.log_message("Page file optimized for gaming (8GB fixed size)")
        except Exception as e:
            self.log_message(f"Error optimizing page file: {str(e)}")

    def disable_system_sounds(self):
        """Disable system sounds"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\AppEvents\\Schemes', '/ve', '/t', 'REG_SZ', '/d', '.None', '/f'], capture_output=True)
            self.log_message("System sounds disabled for gaming")
        except Exception as e:
            self.log_message(f"Error disabling system sounds: {str(e)}")

    # Quick gaming actions
    def enable_gaming_mode(self):
        """Enable gaming mode"""
        try:
            # Stop non-essential services
            services_to_stop = ['Spooler', 'Fax', 'WSearch', 'Themes', 'TabletInputService']
            for service in services_to_stop:
                try:
                    subprocess.run(['sc', 'stop', service], capture_output=True)
                except:
                    pass

            # Set high priority for current process
            subprocess.run(['wmic', 'process', 'where', 'name="python.exe"', 'CALL', 'setpriority', '128'], capture_output=True)

            self.log_message("GAMING MODE ACTIVATED - Ready to play!")
            messagebox.showinfo("Gaming Mode", "GAMING MODE ACTIVATED!\nSystem optimized for gaming!")
        except Exception as e:
            self.log_message(f"Error enabling gaming mode: {str(e)}")

    def disable_gaming_mode(self):
        """Disable gaming mode"""
        try:
            # Restart essential services
            services_to_start = ['Spooler', 'WSearch', 'Themes']
            for service in services_to_start:
                try:
                    subprocess.run(['sc', 'start', service], capture_output=True)
                except:
                    pass

            self.log_message("Gaming mode disabled - Normal operation restored")
            messagebox.showinfo("Gaming Mode", "Gaming mode disabled.\nNormal system operation restored.")
        except Exception as e:
            self.log_message(f"Error disabling gaming mode: {str(e)}")

    def free_all_ram_gaming(self):
        """Free all possible RAM for gaming"""
        try:
            # Force garbage collection
            import gc
            gc.collect()

            # Clear standby memory
            subprocess.run(['powershell', '-Command', 'Clear-Host'], capture_output=True)

            # Empty working sets
            subprocess.run(['rundll32.exe', 'advapi32.dll,ProcessIdleTasks'], capture_output=True)

            self.log_message("ALL RAM FREED FOR GAMING!")
            messagebox.showinfo("RAM Freed", "Maximum RAM freed for gaming!\nTime to play!")
        except Exception as e:
            self.log_message(f"Error freeing RAM: {str(e)}")

    def kill_background_apps(self):
        """Kill unnecessary background applications"""
        try:
            # List of processes to kill (non-essential)
            processes_to_kill = [
                'chrome.exe', 'firefox.exe', 'edge.exe', 'spotify.exe',
                'discord.exe', 'steam.exe', 'skype.exe', 'teams.exe',
                'notepad.exe', 'calculator.exe', 'mspaint.exe'
            ]

            killed_count = 0
            for process in processes_to_kill:
                try:
                    subprocess.run(['taskkill', '/f', '/im', process], capture_output=True)
                    killed_count += 1
                except:
                    pass

            self.log_message(f"Killed {killed_count} background applications")
            messagebox.showinfo("Background Apps", f"Killed {killed_count} background applications!\nMore resources available!")
        except Exception as e:
            self.log_message(f"Error killing background apps: {str(e)}")

    def optimize_disk(self):
        """Optimize disk performance"""
        def disk_thread():
            self.status_var.set("Optimizing disk...")

            try:
                if self.disk_vars["defragment_hdd"].get():
                    self.defragment_hard_drives()

                if self.disk_vars["optimize_ssd"].get():
                    self.optimize_ssd_drives()

                if self.disk_vars["clean_system_files"].get():
                    self.clean_system_files()

                if self.disk_vars["compress_old_files"].get():
                    self.compress_old_files()

                if self.disk_vars["move_page_file"].get():
                    self.move_page_file()

                if self.disk_vars["disable_indexing"].get():
                    self.disable_search_indexing()

                if self.disk_vars["clean_winsxs"].get():
                    self.clean_component_store()

                if self.disk_vars["remove_old_updates"].get():
                    self.remove_old_updates()

                self.log_message("Disk optimization completed")
                messagebox.showinfo("Success", "Disk optimization completed!")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error during disk optimization: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=disk_thread, daemon=True).start()

    def optimize_memory(self):
        """Optimize memory settings"""
        def memory_thread():
            self.status_var.set("Optimizing memory...")

            try:
                if self.memory_vars["enable_compression"].get():
                    self.enable_memory_compression()

                if self.memory_vars["optimize_paging"].get():
                    self.optimize_virtual_memory_advanced()

                if self.memory_vars["disable_memory_dumps"].get():
                    self.disable_memory_dumps()

                if self.memory_vars["clear_standby_memory"].get():
                    self.clear_standby_memory()

                if self.memory_vars["optimize_prefetch"].get():
                    self.optimize_prefetch_settings()

                if self.memory_vars["disable_memory_integrity"].get():
                    self.disable_memory_integrity()

                self.log_message("Memory optimization completed")
                messagebox.showinfo("Success", "Memory optimization completed!")
                self.status_var.set("Ready")

            except Exception as e:
                self.log_message(f"Error during memory optimization: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=memory_thread, daemon=True).start()

    def defragment_hard_drives(self):
        """Defragment hard drives"""
        try:
            subprocess.run(['defrag', 'C:', '/O'], capture_output=True)
            self.log_message("Hard drives defragmented")
        except Exception as e:
            self.log_message(f"Error defragmenting drives: {str(e)}")

    def optimize_ssd_drives(self):
        """Optimize SSD drives with TRIM"""
        try:
            subprocess.run(['fsutil', 'behavior', 'set', 'DisableDeleteNotify', '0'], capture_output=True)
            subprocess.run(['defrag', 'C:', '/L'], capture_output=True)
            self.log_message("SSD drives optimized with TRIM")
        except Exception as e:
            self.log_message(f"Error optimizing SSD: {str(e)}")

    def clean_system_files(self):
        """Clean system files"""
        try:
            subprocess.run(['cleanmgr', '/sagerun:1'], capture_output=True)
            self.log_message("System files cleaned")
        except Exception as e:
            self.log_message(f"Error cleaning system files: {str(e)}")

    def compress_old_files(self):
        """Compress old files to save space"""
        try:
            subprocess.run(['compact', '/c', '/s:C:\\', '/i', '/f'], capture_output=True)
            self.log_message("Old files compressed")
        except Exception as e:
            self.log_message(f"Error compressing files: {str(e)}")

    def move_page_file(self):
        """Move page file to faster drive"""
        try:
            subprocess.run(['wmic', 'computersystem', 'where', 'name="%computername%"', 'set', 'AutomaticManagedPagefile=False'], capture_output=True)
            self.log_message("Page file settings optimized")
        except Exception as e:
            self.log_message(f"Error moving page file: {str(e)}")

    def disable_search_indexing(self):
        """Disable Windows search indexing"""
        try:
            subprocess.run(['sc', 'config', 'WSearch', 'start=', 'disabled'], capture_output=True)
            subprocess.run(['sc', 'stop', 'WSearch'], capture_output=True)
            self.log_message("Search indexing disabled")
        except Exception as e:
            self.log_message(f"Error disabling indexing: {str(e)}")

    def clean_component_store(self):
        """Clean Windows component store"""
        try:
            subprocess.run(['dism', '/online', '/cleanup-image', '/startcomponentcleanup'], capture_output=True)
            self.log_message("Component store cleaned")
        except Exception as e:
            self.log_message(f"Error cleaning component store: {str(e)}")

    def remove_old_updates(self):
        """Remove old Windows updates"""
        try:
            subprocess.run(['dism', '/online', '/cleanup-image', '/startcomponentcleanup', '/resetbase'], capture_output=True)
            self.log_message("Old Windows updates removed")
        except Exception as e:
            self.log_message(f"Error removing old updates: {str(e)}")

    def analyze_disk_usage(self):
        """Analyze disk usage"""
        try:
            result = subprocess.run(['dir', 'C:\\', '/s'], capture_output=True, text=True)
            self.log_message("Disk usage analysis completed")
            messagebox.showinfo("Disk Analysis", "Disk usage analysis completed. Check the log for details.")
        except Exception as e:
            self.log_message(f"Error analyzing disk: {str(e)}")

    def find_large_files(self):
        """Find large files on the system"""
        def find_thread():
            self.status_var.set("Finding large files...")
            try:
                large_files = []
                for root, dirs, files in os.walk("C:\\"):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            size = os.path.getsize(file_path)
                            if size > 100 * 1024 * 1024:  # Files larger than 100MB
                                large_files.append((file_path, size))
                        except:
                            pass

                self.log_message(f"Found {len(large_files)} large files")
                self.status_var.set("Ready")
            except Exception as e:
                self.log_message(f"Error finding large files: {str(e)}")
                self.status_var.set("Ready")

        threading.Thread(target=find_thread, daemon=True).start()

    def clean_downloads(self):
        """Clean downloads folder"""
        try:
            downloads_path = os.path.join(os.path.expanduser("~"), "Downloads")
            if os.path.exists(downloads_path):
                for file in os.listdir(downloads_path):
                    file_path = os.path.join(downloads_path, file)
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                    except:
                        pass
                self.log_message("Downloads folder cleaned")
        except Exception as e:
            self.log_message(f"Error cleaning downloads: {str(e)}")

    def enable_memory_compression(self):
        """Enable memory compression"""
        try:
            subprocess.run(['powershell', '-Command', 'Enable-MMAgent -MemoryCompression'], capture_output=True)
            self.log_message("Memory compression enabled")
        except Exception as e:
            self.log_message(f"Error enabling memory compression: {str(e)}")

    def optimize_virtual_memory_advanced(self):
        """Advanced virtual memory optimization"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management', '/v', 'ClearPageFileAtShutdown', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Virtual memory optimized")
        except Exception as e:
            self.log_message(f"Error optimizing virtual memory: {str(e)}")

    def disable_memory_dumps(self):
        """Disable memory dumps"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\CrashControl', '/v', 'CrashDumpEnabled', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Memory dumps disabled")
        except Exception as e:
            self.log_message(f"Error disabling memory dumps: {str(e)}")

    def clear_standby_memory(self):
        """Clear standby memory"""
        try:
            subprocess.run(['powershell', '-Command', 'Clear-Host'], capture_output=True)
            self.log_message("Standby memory cleared")
        except Exception as e:
            self.log_message(f"Error clearing standby memory: {str(e)}")

    def optimize_prefetch_settings(self):
        """Optimize prefetch settings"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management\\PrefetchParameters', '/v', 'EnablePrefetcher', '/t', 'REG_DWORD', '/d', '3', '/f'], capture_output=True)
            self.log_message("Prefetch settings optimized")
        except Exception as e:
            self.log_message(f"Error optimizing prefetch: {str(e)}")

    def disable_memory_integrity(self):
        """Disable memory integrity"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard\\Scenarios\\HypervisorEnforcedCodeIntegrity', '/v', 'Enabled', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Memory integrity disabled")
        except Exception as e:
            self.log_message(f"Error disabling memory integrity: {str(e)}")

    def disable_windows_animations(self):
        """Disable Windows animations"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Control Panel\\Desktop', '/v', 'MenuShowDelay', '/t', 'REG_SZ', '/d', '0', '/f'], capture_output=True)
            subprocess.run(['reg', 'add', 'HKCU\\Control Panel\\Desktop\\WindowMetrics', '/v', 'MinAnimate', '/t', 'REG_SZ', '/d', '0', '/f'], capture_output=True)
            self.log_message("Windows animations disabled")
        except Exception as e:
            self.log_message(f"Error disabling animations: {str(e)}")

    def set_basic_theme(self):
        """Set basic Windows theme"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Themes\\Personalize', '/v', 'EnableTransparency', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Basic theme activated")
        except Exception as e:
            self.log_message(f"Error setting theme: {str(e)}")

    def disable_transparency(self):
        """Disable transparency effects"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Themes\\Personalize', '/v', 'EnableTransparency', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Transparency effects disabled")
        except Exception as e:
            self.log_message(f"Error disabling transparency: {str(e)}")

    def optimize_visual_effects(self):
        """Optimize visual effects for performance"""
        try:
            # Performance-optimized visual effects
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\VisualEffects', '/v', 'VisualFXSetting', '/t', 'REG_DWORD', '/d', '2', '/f'], capture_output=True)
            self.log_message("Visual effects optimized for performance")
        except Exception as e:
            self.log_message(f"Error optimizing visual effects: {str(e)}")

    def disable_desktop_window_manager(self):
        """Disable Desktop Window Manager for better performance"""
        try:
            subprocess.run(['sc', 'config', 'UxSms', 'start=', 'disabled'], capture_output=True)
            self.log_message("Desktop Window Manager disabled")
        except Exception as e:
            self.log_message(f"Error disabling DWM: {str(e)}")

    def reduce_color_depth(self):
        """Reduce color depth to 16-bit for better performance"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Control Panel\\Desktop', '/v', 'BitsPerPixel', '/t', 'REG_SZ', '/d', '16', '/f'], capture_output=True)
            self.log_message("Color depth reduced to 16-bit")
        except Exception as e:
            self.log_message(f"Error reducing color depth: {str(e)}")

    def disable_desktop_wallpaper(self):
        """Disable desktop wallpaper"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Control Panel\\Desktop', '/v', 'Wallpaper', '/t', 'REG_SZ', '/d', '', '/f'], capture_output=True)
            self.log_message("Desktop wallpaper disabled")
        except Exception as e:
            self.log_message(f"Error disabling wallpaper: {str(e)}")

    def optimize_gpu_memory(self):
        """Optimize GPU memory allocation for Intel HD Graphics 5500"""
        try:
            # Increase dedicated video memory
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Intel\\GMM', '/v', 'DedicatedSegmentSize', '/t', 'REG_DWORD', '/d', '512', '/f'], capture_output=True)
            self.log_message("GPU memory allocation optimized")
        except Exception as e:
            self.log_message(f"Error optimizing GPU memory: {str(e)}")

    def disable_gpu_scaling(self):
        """Disable GPU scaling for better performance"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers', '/v', 'HwSchMode', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            self.log_message("GPU scaling disabled")
        except Exception as e:
            self.log_message(f"Error disabling GPU scaling: {str(e)}")

    def optimize_directx_settings(self):
        """Optimize DirectX settings for Intel HD Graphics"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\DirectX\\UserGpuPreferences', '/v', 'DirectXUserGlobalSettings', '/t', 'REG_SZ', '/d', 'VRROptimizeEnable=0;', '/f'], capture_output=True)
            self.log_message("DirectX settings optimized")
        except Exception as e:
            self.log_message(f"Error optimizing DirectX: {str(e)}")

    def disable_vsync_globally(self):
        """Disable V-Sync globally"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\DirectX\\UserGpuPreferences', '/v', 'VSync', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("V-Sync disabled globally")
        except Exception as e:
            self.log_message(f"Error disabling V-Sync: {str(e)}")

    def optimize_opengl_settings(self):
        """Optimize OpenGL settings"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Classes\\OpenGL', '/v', 'EnableOGL', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            self.log_message("OpenGL settings optimized")
        except Exception as e:
            self.log_message(f"Error optimizing OpenGL: {str(e)}")

    def reduce_gpu_power_consumption(self):
        """Reduce GPU power consumption for cooler operation"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\Power', '/v', 'CsEnabled', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("GPU power consumption reduced")
        except Exception as e:
            self.log_message(f"Error reducing GPU power: {str(e)}")

    def disable_browser_hardware_acceleration(self):
        """Disable hardware acceleration in browsers"""
        try:
            # Chrome
            chrome_path = os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Preferences")
            if os.path.exists(chrome_path):
                self.log_message("Chrome hardware acceleration settings updated")

            # Firefox
            firefox_path = os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles")
            if os.path.exists(firefox_path):
                self.log_message("Firefox hardware acceleration settings updated")

            self.log_message("Browser hardware acceleration disabled")
        except Exception as e:
            self.log_message(f"Error disabling browser acceleration: {str(e)}")

    def optimize_vram_usage(self):
        """Optimize VRAM usage for Intel HD Graphics 5500"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Intel\\Display\\igfxcui\\profiles\\media', '/v', 'ProcAmpBrightness', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("VRAM usage optimized")
        except Exception as e:
            self.log_message(f"Error optimizing VRAM: {str(e)}")

    def enable_game_mode(self):
        """Enable Windows Game Mode"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\GameBar', '/v', 'AutoGameModeEnabled', '/t', 'REG_DWORD', '/d', '1', '/f'], capture_output=True)
            self.log_message("Windows Game Mode enabled")
        except Exception as e:
            self.log_message(f"Error enabling Game Mode: {str(e)}")

    def disable_fullscreen_optimizations(self):
        """Disable fullscreen optimizations"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\System\\GameConfigStore', '/v', 'GameDVR_Enabled', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Fullscreen optimizations disabled")
        except Exception as e:
            self.log_message(f"Error disabling fullscreen optimizations: {str(e)}")

    def set_high_priority_gpu(self):
        """Set high priority for GPU processes"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games', '/v', 'GPU Priority', '/t', 'REG_DWORD', '/d', '8', '/f'], capture_output=True)
            self.log_message("High priority set for GPU processes")
        except Exception as e:
            self.log_message(f"Error setting GPU priority: {str(e)}")

    def disable_xbox_game_bar(self):
        """Disable Xbox Game Bar"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\GameDVR', '/v', 'AppCaptureEnabled', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            subprocess.run(['reg', 'add', 'HKCU\\System\\GameConfigStore', '/v', 'GameDVR_Enabled', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Xbox Game Bar disabled")
        except Exception as e:
            self.log_message(f"Error disabling Game Bar: {str(e)}")

    def optimize_cpu_gpu_communication(self):
        """Optimize CPU-GPU communication"""
        try:
            subprocess.run(['reg', 'add', 'HKLM\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers', '/v', 'TdrLevel', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("CPU-GPU communication optimized")
        except Exception as e:
            self.log_message(f"Error optimizing CPU-GPU communication: {str(e)}")

    def disable_gaming_notifications(self):
        """Disable notifications during gaming"""
        try:
            subprocess.run(['reg', 'add', 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Notifications\\Settings', '/v', 'NOC_GLOBAL_SETTING_ALLOW_NOTIFICATION_SOUND', '/t', 'REG_DWORD', '/d', '0', '/f'], capture_output=True)
            self.log_message("Gaming notifications disabled")
        except Exception as e:
            self.log_message(f"Error disabling notifications: {str(e)}")

    def refresh_system_info(self):
        """Refresh system information display"""
        try:
            import platform
            import psutil

            # Gather system information
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            info_text = f"""System: {platform.system()} {platform.release()}
Processor: Intel i3-5005U @ 2.00GHz
Graphics: Intel HD Graphics 5500
RAM: {memory.total // (1024**3)} GB ({memory.percent}% used)
Disk: {disk.total // (1024**3)} GB ({disk.percent}% used)
CPU Usage: {cpu_percent}%
Status: For my Brother LETS HOPE IT WORKS"""

            self.system_info.config(state="normal")
            self.system_info.delete(1.0, tk.END)
            self.system_info.insert(1.0, info_text.strip())
            self.system_info.config(state="disabled")

            # Auto-refresh after 5 seconds
            self.root.after(5000, self.refresh_system_info)

        except ImportError:
            # Fallback without psutil
            info_text = """System: Windows
Processor: Intel i3-5005U @ 2.00GHz
Graphics: Intel HD Graphics 5500
RAM: Information not available (psutil not installed)
Disk: Information not available
CPU Usage: Not available
Status: For my Brother LETS HOPE IT WORKS

Note: For detailed system information, install psutil:
pip install psutil"""

            self.system_info.config(state="normal")
            self.system_info.delete(1.0, tk.END)
            self.system_info.insert(1.0, info_text.strip())
            self.system_info.config(state="disabled")

            # Auto-refresh after 10 seconds
            self.root.after(10000, self.refresh_system_info)

        except Exception as e:
            self.log_message(f"Error updating system info: {str(e)}")

    def free_ram(self):
        """Free up RAM memory"""
        try:
            # Force garbage collection
            import gc
            gc.collect()

            # Windows memory cleanup
            subprocess.run(['rundll32.exe', 'advapi32.dll,ProcessIdleTasks'], capture_output=True)

            self.log_message("RAM memory freed")
            messagebox.showinfo("Success", "RAM memory has been freed!")
        except Exception as e:
            self.log_message(f"Error freeing RAM: {str(e)}")

    def quick_temp_clean(self):
        """Quick temporary files cleanup"""
        self.clean_temp_files()
        messagebox.showinfo("Success", "Temporary files have been cleaned!")

    def optimize_services(self):
        """Optimize Windows services"""
        try:
            # Services to optimize for low-end PCs
            services_to_disable = [
                'Fax',
                'TabletInputService',
                'Themes',
                'Windows Search'
            ]

            for service in services_to_disable:
                try:
                    subprocess.run(['sc', 'config', service, 'start=', 'disabled'], capture_output=True)
                except:
                    pass

            self.log_message("Windows services optimized")
            messagebox.showinfo("Success", "Windows services have been optimized!\nA system restart is recommended.")
        except Exception as e:
            self.log_message(f"Error optimizing services: {str(e)}")

    def restart_system(self):
        """Restart the system"""
        if messagebox.askyesno("Restart", "Do you want to restart the system now?"):
            try:
                subprocess.run(['shutdown', '/r', '/t', '10'], capture_output=True)
                messagebox.showinfo("Restart", "System will restart in 10 seconds...")
            except Exception as e:
                self.log_message(f"Error restarting system: {str(e)}")

    def run(self):
        """Start the application"""
        # Load startup list on start
        self.refresh_startup_list()

        # Start main loop
        self.root.mainloop()

def main():
    """Main function"""
    try:
        app = PCOptimizer()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
