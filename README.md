# PC Optimizer - Low-End Edition

A specially developed PC optimization application for low-end hardware, optimized for:
- **Intel i3-5005U CPU @ 2.00GHz**
- **Intel HD Graphics 5500**

## Features

### System Cleanup
- Delete temporary files
- Clear browser cache
- Empty recycle bin automatically
- Clean prefetch files
- Remove log files

### Startup Manager
- Display and manage startup programs
- Evaluate performance impact
- Disable unnecessary programs

### Graphics Optimization (Intel HD Graphics 5500)
**Basic Graphics Settings:**
- Disable Windows animations
- Enable basic theme
- Turn off transparency effects
- Optimize visual effects for performance
- Disable Desktop Window Manager
- Reduce color depth to 16-bit
- Disable desktop wallpaper

**Intel HD Graphics 5500 Specific:**
- Optimize GPU memory allocation
- Disable GPU scaling
- Optimize DirectX settings
- Disable V-Sync globally
- Optimize OpenGL settings
- Reduce GPU power consumption
- Disable browser hardware acceleration
- Optimize VRAM usage

**Gaming Performance Mode:**
- Enable Windows Game Mode
- Disable fullscreen optimizations
- Set high priority for GPU processes
- Disable Xbox Game Bar
- Optimize CPU-GPU communication
- Disable notifications during gaming

### Programs Manager
- Scan for unused programs
- Find and remove duplicate files
- Uninstall suspicious software
- Free up disk space automatically

### Registry Cleaner
- Remove invalid registry entries
- Fix broken shortcuts
- Delete empty registry keys
- Clean startup registry entries
- Create and restore registry backups

### Network Optimizer
- DNS optimization (Google, Cloudflare, OpenDNS)
- TCP/IP stack optimization
- Disable Nagle algorithm
- Increase network buffer size
- Disable bandwidth throttling
- Optimize MTU size

### Disk Optimization
- Defragment hard drives
- Optimize SSD drives (TRIM)
- Clean system files
- Compress old files
- Move page file to faster drive
- Disable search indexing
- Clean Windows component store
- Remove old Windows updates
- Analyze disk usage
- Find large files
- Clean downloads folder

### Memory Optimization
- Enable memory compression
- Optimize virtual memory
- Disable memory dumps
- Clear standby memory
- Optimize prefetch settings
- Disable memory integrity

### Advanced System Tweaks
**Privacy and Performance:**
- Disable Windows telemetry
- Disable Cortana
- Disable Windows Defender real-time protection
- Disable automatic Windows updates
- Disable error reporting
- Disable location tracking
- Disable Windows ads and suggestions
- Disable feedback requests
- Disable Windows Timeline
- Disable activity history

**System Performance:**
- Disable hibernation
- Set high performance power plan
- Disable system restore
- Optimize virtual memory
- Disable Superfetch service

**Bloatware Removal:**
- Scan for Windows bloatware
- Remove unnecessary Windows apps

### System Monitor
- Live CPU and RAM usage
- Disk status
- Hardware-specific information

### Quick Actions
- Free RAM instantly
- Quick temp file cleanup
- Optimize Windows services
- Restart system

## Installation and Usage

### Requirements
- Python 3.7 or higher
- Windows operating system
- Administrator rights (for some optimizations)

### Optional Dependencies
For extended system information:
```bash
pip install psutil
```

### Start Application
```bash
python pc_optimizer.py
```

## Specifically Optimized for Low-End PCs

This application was specifically developed for older hardware:

- **Minimal resource consumption**: Lightweight tkinter GUI
- **Hardware-specific optimizations**: Tailored for Intel i3-5005U and HD Graphics 5500
- **Smart cleanup routines**: Focus on the most important performance improvements
- **Safe operations**: All changes are reversible

## Important Notes

- **Administrator rights**: Some functions require elevated privileges
- **Backup**: Create a system backup before major optimizations
- **Restart**: A restart is recommended after graphics optimizations

## Usage

1. **Cleanup Tab**: Select desired cleanup options and click "Start System Cleanup"
2. **Startup Tab**: Review startup programs and disable unnecessary ones
3. **Graphics Tab**: Apply comprehensive graphics optimizations for Intel HD Graphics 5500
4. **Programs Tab**: Scan for unused programs and duplicate files, then remove them
5. **Registry Tab**: Clean registry entries and create backups for safety
6. **Network Tab**: Optimize DNS settings and network performance
7. **Disk Tab**: Optimize disk performance and manage storage
8. **GAMING Tab**: EXTRA gaming optimizations specifically for Minecraft and low-end hardware
9. **Advanced Tab**: Apply advanced system tweaks and remove bloatware
10. **System Tab**: Monitor system performance and use quick actions

## New Advanced Features

### Unused Programs Scanner
- Automatically detects programs that haven't been used recently
- Identifies suspicious software and toolbars
- Shows installation dates and sizes
- Safe uninstallation process

### Duplicate File Finder
- Scans common directories for duplicate files
- Shows file sizes and modification dates
- Safe deletion with confirmation
- Frees up significant disk space

### Registry Optimization
- Removes invalid registry entries
- Fixes broken shortcuts automatically
- Creates registry backups before changes
- Cleans startup registry entries

### Network Performance Boost
- Multiple DNS options for faster browsing
- TCP/IP stack optimization
- Bandwidth throttling removal
- MTU size optimization

### Privacy and Performance
- Disable Windows telemetry and tracking
- Remove Cortana and unnecessary features
- Uninstall Windows bloatware apps
- Optimize power settings for performance

## Clean UI Design

The user interface was deliberately designed to be simple and resource-efficient:
- **Large window size (1000x800)** for better visibility
- **Scrollable tabs** for easy navigation through all options
- **Dark theme** for less eye strain
- **Clear tab structure** with organized sections
- **Simple labels and descriptions** in human-readable English
- **Minimal memory usage** optimized for low-end hardware
- **Centered window** that adapts to screen size
- **Mouse wheel scrolling** support for smooth navigation

## Performance Tips for Intel i3-5005U

- Disable unnecessary startup programs
- Use the basic Windows theme
- Turn off transparency effects
- Regularly clean temporary files
- Optimize Windows services

## Support

For problems or questions you can:
- Check the log output in the application
- Run the application as administrator
- Create a system backup before major changes

---

**Developed specifically for low-end PCs with Intel i3-5005U and HD Graphics 5500**
